README.md
pyproject.toml
reverie_cli/__init__.py
reverie_cli/main.py
reverie_cli.egg-info/PKG-INFO
reverie_cli.egg-info/SOURCES.txt
reverie_cli.egg-info/dependency_links.txt
reverie_cli.egg-info/entry_points.txt
reverie_cli.egg-info/requires.txt
reverie_cli.egg-info/top_level.txt
reverie_cli/agent/__init__.py
reverie_cli/agent/engine.py
reverie_cli/agent/executor.py
reverie_cli/agent/learning.py
reverie_cli/agent/memory.py
reverie_cli/agent/planner.py
reverie_cli/agent/prompts.py
reverie_cli/api/__init__.py
reverie_cli/api/dependencies.py
reverie_cli/api/models.py
reverie_cli/api/server.py
reverie_cli/api/routes/__init__.py
reverie_cli/api/routes/chat.py
reverie_cli/api/routes/config.py
reverie_cli/api/routes/enhanced_agent.py
reverie_cli/api/routes/files.py
reverie_cli/api/routes/health.py
reverie_cli/api/routes/models.py
reverie_cli/api/routes/tools.py
reverie_cli/api/routes/web.py
reverie_cli/core/__init__.py
reverie_cli/core/config.py
reverie_cli/core/config_manager.py
reverie_cli/core/console.py
reverie_cli/core/exceptions.py
reverie_cli/core/logging.py
reverie_cli/models/__init__.py
reverie_cli/models/backends.py
reverie_cli/models/detector.py
reverie_cli/models/info.py
reverie_cli/models/llama_cpp_manager.py
reverie_cli/models/manager.py
reverie_cli/models/presets.py
reverie_cli/static/js/main.js
reverie_cli/static/js/models.js
reverie_cli/templates/index.html
reverie_cli/tools/__init__.py
reverie_cli/tools/base.py
reverie_cli/tools/code_execution.py
reverie_cli/tools/context_engine.py
reverie_cli/tools/file_operations.py
reverie_cli/tools/manager.py
reverie_cli/tools/memory_engine.py
reverie_cli/tools/registry.py
reverie_cli/tools/web_engine.py
tests/test_enhanced_features.py