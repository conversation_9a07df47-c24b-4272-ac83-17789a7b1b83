"""
Enhanced Interactive Console for Reverie CLI.

This module provides a dual-mode interactive console that allows users to:
- Direct AI Coder Mode: Direct AI assistance for coding tasks
- API Service Mode: Server management and monitoring
- Unified interface supporting both modes simultaneously
- Real-time command processing and execution
- AI-powered command suggestions and auto-completion
- Memory-enhanced interactions with learning capabilities
"""

import asyncio
import sys
import json
import subprocess
import threading
import difflib
import re
from typing import Dict, List, Optional, Any, Callable, Tuple
from pathlib import Path
from datetime import datetime

from prompt_toolkit import PromptSession
from prompt_toolkit.completion import WordCompleter, Completer, Completion
from prompt_toolkit.history import FileHistory
from prompt_toolkit.shortcuts import confirm
from prompt_toolkit.formatted_text import HTML
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.live import Live
from rich.layout import Layout
from rich.columns import Columns
from rich.status import Status

from reverie_cli import get_logger, get_settings
from reverie_cli.core.exceptions import ReverieError


class EnhancedAICompleter(Completer):
    """AI-powered command completer with context awareness and intelligent suggestions."""

    def __init__(self, console_instance):
        self.console = console_instance
        self.base_commands = [
            "help", "models", "server", "tools", "config", "exit", "quit",
            "code", "analyze", "search", "remember", "web", "context", "ai",
            "status", "clear", "mode", "chat", "review", "test", "refactor",
            "load", "unload"  # Add load and unload to base commands
        ]

        # Command aliases and shortcuts
        self.command_aliases = {
            "ls": "models list",
            "list": "models list",
            "show": "models list",
            "start": "models load",
            "run": "models load",
            "stop": "models unload",
            "q": "exit",
            "h": "help",
            "?": "help",
        }

    def get_completions(self, document, complete_event):
        text = document.text_before_cursor
        words = text.split()

        if not words:
            # Show all base commands and aliases
            for cmd in self.base_commands:
                yield Completion(cmd, start_position=0)
            for alias in self.command_aliases.keys():
                yield Completion(alias, start_position=0, display_meta=f"→ {self.command_aliases[alias]}")
        else:
            current_word = words[-1] if text.endswith(' ') else words[-1] if words else ''

            # Smart completion based on context
            if len(words) == 1 and not text.endswith(' '):
                # Complete command names and aliases
                for cmd in self.base_commands:
                    if cmd.startswith(current_word.lower()):
                        yield Completion(cmd, start_position=-len(current_word))

                # Complete aliases
                for alias, full_cmd in self.command_aliases.items():
                    if alias.startswith(current_word.lower()):
                        yield Completion(alias, start_position=-len(current_word),
                                       display_meta=f"→ {full_cmd}")

                # Fuzzy matching for typos
                if current_word:
                    close_matches = difflib.get_close_matches(
                        current_word.lower(), self.base_commands, n=3, cutoff=0.6
                    )
                    for match in close_matches:
                        if match not in [cmd for cmd in self.base_commands if cmd.startswith(current_word.lower())]:
                            yield Completion(match, start_position=-len(current_word),
                                           display_meta="(suggestion)")
            else:
                # Context-aware completion for command arguments
                command = words[0].lower()

                # Handle aliases
                if command in self.command_aliases:
                    alias_parts = self.command_aliases[command].split()
                    command = alias_parts[0]

                if command == "models":
                    subcommands = ["list", "load", "unload", "status", "scan", "info"]
                    for subcommand in subcommands:
                        if subcommand.startswith(current_word.lower()):
                            yield Completion(subcommand, start_position=-len(current_word))

                    # If we're completing model names for load
                    if len(words) >= 2 and words[1].lower() == "load":
                        yield from self._get_model_completions(current_word)

                elif command == "load":
                    # Smart model name completion for direct load command
                    yield from self._get_model_completions(current_word)
                elif command == "server":
                    for subcommand in ["status", "stop", "restart", "logs", "stats"]:
                        if subcommand.startswith(current_word.lower()):
                            yield Completion(subcommand, start_position=-len(current_word))
                elif command == "code":
                    for subcommand in ["create", "edit", "analyze", "review", "test"]:
                        if subcommand.startswith(current_word.lower()):
                            yield Completion(subcommand, start_position=-len(current_word))
                elif command == "help":
                    # Complete with available commands for help
                    for cmd in self.base_commands:
                        if cmd.startswith(current_word.lower()):
                            yield Completion(cmd, start_position=-len(current_word))

    def _get_model_completions(self, current_word: str):
        """Get intelligent model name completions with status and metadata."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()
            available_models = model_manager.list_models()

            # Sort models by relevance: loaded first, then by name
            sorted_models = sorted(available_models, key=lambda m: (
                0 if m.status.value == "loaded" else 1,
                m.name.lower()
            ))

            for model_info in sorted_models:
                model_name = model_info.name
                if not current_word or model_name.lower().startswith(current_word.lower()):
                    # Create completion with rich metadata
                    status_icon = {
                        "available": "⚪",
                        "loaded": "🟢",
                        "loading": "🟡",
                        "error": "🔴"
                    }.get(model_info.status.value, "❓")

                    # Show model name with status and backend info
                    display_text = f"{model_name} {status_icon} ({model_info.backend.value}"
                    if model_info.size_gb:
                        display_text += f", {model_info.size_gb:.1f}GB"
                    display_text += ")"

                    yield Completion(
                        model_name,
                        start_position=-len(current_word) if current_word else 0,
                        display=display_text
                    )

            # If no exact matches, try fuzzy matching
            if current_word and not any(m.name.lower().startswith(current_word.lower()) for m in available_models):
                model_names = [m.name for m in available_models]
                close_matches = difflib.get_close_matches(current_word, model_names, n=3, cutoff=0.6)
                for match in close_matches:
                    model_info = next(m for m in available_models if m.name == match)
                    status_icon = {
                        "available": "⚪",
                        "loaded": "🟢",
                        "loading": "🟡",
                        "error": "🔴"
                    }.get(model_info.status.value, "❓")

                    yield Completion(
                        match,
                        start_position=-len(current_word),
                        display=f"{match} {status_icon} (suggestion)"
                    )

        except Exception as e:
            # Fallback: if model manager fails, don't show any completions
            # This prevents crashes during completion
            pass


class SmartReverieCompleter(Completer):
    """Smart command completer for Reverie CLI with model-aware completion."""

    def __init__(self, console_instance):
        self.console = console_instance
        self.base_commands = list(console_instance.commands.keys())

    def get_completions(self, document, complete_event):
        text = document.text_before_cursor
        words = text.split()

        if not words:
            # Show all base commands
            for cmd in self.base_commands:
                yield Completion(cmd, start_position=0)
        elif len(words) == 1 and not text.endswith(' '):
            # Complete command names
            current_word = words[0]
            for cmd in self.base_commands:
                if cmd.startswith(current_word.lower()):
                    yield Completion(cmd, start_position=-len(current_word))
        else:
            # Context-aware completion for command arguments
            command = words[0].lower()
            current_word = words[-1] if not text.endswith(' ') else ''

            if command == "load":
                # Smart model name completion for load command
                yield from self._get_model_completions(current_word)
            elif command == "models":
                # Subcommands for models command
                subcommands = ["list", "load", "unload", "status", "scan", "info"]
                for subcmd in subcommands:
                    if subcmd.startswith(current_word.lower()):
                        yield Completion(subcmd, start_position=-len(current_word))
            elif command == "download":
                # Download options
                download_options = ["llama-cpp"]
                for option in download_options:
                    if option.startswith(current_word.lower()):
                        yield Completion(option, start_position=-len(current_word))

    def _get_model_completions(self, current_word: str):
        """Get model name completions for load command."""
        try:
            from reverie_cli.models.manager import get_model_manager
            import asyncio

            model_manager = get_model_manager()

            # Check if we have models, if not try to scan
            available_models = model_manager.list_models()
            if not available_models:
                # Try to scan for models synchronously
                try:
                    # Create a new event loop for scanning if needed
                    loop = None
                    try:
                        loop = asyncio.get_event_loop()
                    except RuntimeError:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                    if loop.is_running():
                        # If loop is running, we can't use run_until_complete
                        # Just return empty completions to avoid blocking
                        return
                    else:
                        loop.run_until_complete(model_manager.scan_local_models())
                        available_models = model_manager.list_models()
                except Exception:
                    # If scanning fails, just return empty
                    return

            # Sort models: loaded first, then available, then others
            def sort_key(model_info):
                status_priority = {
                    "loaded": 0,
                    "available": 1,
                    "loading": 2,
                    "error": 3
                }
                return (status_priority.get(model_info.status.value, 4), model_info.name.lower())

            sorted_models = sorted(available_models, key=sort_key)

            for model_info in sorted_models:
                model_name = model_info.name
                if model_name.lower().startswith(current_word.lower()):
                    # Create completion with model status indicator
                    status_icon = {
                        "available": "⚪",
                        "loaded": "🟢",
                        "loading": "🟡",
                        "error": "🔴"
                    }.get(model_info.status.value, "❓")

                    # Show model name with status and backend info
                    display_text = f"{model_name} {status_icon} ({model_info.backend.value})"
                    yield Completion(
                        model_name,
                        start_position=-len(current_word),
                        display=display_text
                    )

        except Exception as e:
            # Fallback: if model manager fails, don't show any completions
            # This prevents crashes during completion
            pass


class DualModeConsole:
    """Enhanced dual-mode interactive console for Reverie CLI."""

    def __init__(self):
        self.console = Console()
        self.logger = get_logger("console")
        self.settings = get_settings()

        # Dual mode state
        self.ai_coder_mode = True  # Direct AI Coder mode
        self.api_service_mode = True  # API Service mode
        self.current_context = "general"

        # Enhanced command registry
        self.commands = {
            # Core commands
            "help": self.show_help,
            "exit": self.exit_console,
            "quit": self.exit_console,
            "clear": self.clear_screen,
            "status": self.show_status,

            # AI Coder Mode commands
            "code": self.handle_ai_coding,
            "analyze": self.handle_code_analysis,
            "review": self.handle_code_review,
            "test": self.handle_testing,
            "refactor": self.handle_refactoring,
            "ai": self.handle_ai_chat,

            # API Service Mode commands
            "models": self.handle_models,
            "server": self.handle_server,
            "api": self.handle_api,
            "logs": self.handle_logs,

            # Enhanced tools
            "web": self.handle_web_search,
            "search": self.handle_intelligent_search,
            "remember": self.handle_memory,
            "context": self.handle_context,
            "tools": self.handle_tools,
            "config": self.handle_config,

            # Mode switching
            "mode": self.handle_mode_switch,

            # Shortcuts and aliases
            "load": self.handle_direct_load,
            "unload": self.handle_direct_unload,
            "ls": self.handle_list_shortcut,
        }

        # AI-powered completer
        self.completer = EnhancedAICompleter(self)

        # Enhanced session with AI features
        self.session = PromptSession(
            history=FileHistory(str(Path.home() / ".reverie_enhanced_history")),
            completer=self.completer,
            complete_while_typing=True,
        )

        # State management
        self.running = False
        self.server_status = "unknown"
        self.current_model = None
        self.conversation_history = []

        # Background monitoring
        self.status_monitor_active = False

        # Import enhanced engines
        self._init_enhanced_engines()

    def _init_enhanced_engines(self):
        """Initialize enhanced engines for AI capabilities."""
        try:
            from reverie_cli.tools.web_engine import WebEngine
            from reverie_cli.tools.context_engine import ContextEngine
            from reverie_cli.tools.memory_engine import MemoryEngine

            self.web_engine = WebEngine()
            self.context_engine = ContextEngine()
            self.memory_engine = MemoryEngine()

            self.logger.info("Enhanced engines initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize enhanced engines: {e}")
            self.web_engine = None
            self.context_engine = None
            self.memory_engine = None

    async def run_dual_mode(self):
        """Run the enhanced dual-mode console."""
        self.running = True

        # Display welcome message
        self._display_welcome()

        # Start background monitoring
        if self.api_service_mode:
            self._start_status_monitoring()

        try:
            while self.running:
                try:
                    # Get current prompt based on mode and context
                    prompt_text = self._get_dynamic_prompt()

                    # Get user input with AI-enhanced completion
                    user_input = await self.session.prompt_async(prompt_text)

                    if user_input.strip():
                        # Process command with dual-mode handling
                        await self._process_dual_mode_command(user_input.strip())

                        # Learn from interaction
                        await self._learn_from_interaction(user_input)

                except KeyboardInterrupt:
                    self.console.print("\n[yellow]Use 'exit' or 'quit' to leave the console.[/yellow]")
                except EOFError:
                    break
                except Exception as e:
                    self.logger.error(f"Console error: {e}")
                    self.console.print(f"[red]Error: {e}[/red]")

        finally:
            self._cleanup()

    def _display_welcome(self):
        """Display enhanced welcome message."""
        welcome_panel = Panel(
            Text.from_markup(
                "[bold blue]🚀 Reverie CLI - Enhanced AI-Native Development Server[/bold blue]\n\n"
                "[green]✓ Direct AI Coder Mode: Active[/green]\n"
                "[green]✓ API Service Mode: Active[/green]\n\n"
                "[cyan]Available Commands:[/cyan]\n"
                "• [bold]ai[/bold] - Direct AI assistance\n"
                "• [bold]code[/bold] - AI coding tools\n"
                "• [bold]analyze[/bold] - Code analysis\n"
                "• [bold]web[/bold] - Web search\n"
                "• [bold]search[/bold] - Intelligent search\n"
                "• [bold]remember[/bold] - Memory management\n"
                "• [bold]server[/bold] - Server management\n"
                "• [bold]models[/bold] - Model management\n"
                "• [bold]help[/bold] - Show all commands\n\n"
                "[dim]Type 'help' for detailed command information[/dim]"
            ),
            title="🎯 Reverie CLI Enhanced Console",
            border_style="blue"
        )
        self.console.print(welcome_panel)
        self.console.print()

    def _get_dynamic_prompt(self) -> HTML:
        """Get dynamic prompt based on current mode and context."""
        mode_indicator = "🤖" if self.ai_coder_mode else "⚙️"
        context_color = "cyan" if self.current_context == "general" else "yellow"

        return HTML(
            f'<ansiblue>{mode_indicator}</ansiblue> '
            f'<{context_color}>[{self.current_context}]</{context_color}> '
            f'<ansigreen>reverie</ansigreen><ansired>></ansired> '
        )

    async def _process_dual_mode_command(self, user_input: str):
        """Process command with intelligent error handling and suggestions."""
        if not user_input.strip():
            return

        parts = user_input.strip().split()
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []

        # Handle special cases for model loading shortcuts
        if command == "load" and args:
            # Direct model loading: "load ModelName"
            await self._handle_direct_model_load(args)
            return
        elif command == "models" and args and args[0] == "load":
            # Standard model loading: "models load ModelName"
            if len(args) > 1:
                await self._load_model(args[1])
            else:
                await self._show_model_load_help()
            return

        # Check if it's a direct AI request (natural language)
        if self.ai_coder_mode and command not in self.commands and not self._is_likely_command(command):
            await self._handle_natural_language_request(user_input)
            return

        # Handle registered commands
        if command in self.commands:
            try:
                await self.commands[command](args)
            except Exception as e:
                self.logger.error(f"Command error: {e}")
                await self._show_command_error(command, str(e), args)
        else:
            await self._handle_unknown_command(command, args)

    async def _handle_natural_language_request(self, request: str):
        """Handle natural language AI requests."""
        self.console.print(f"[blue]🤖 Processing AI request:[/blue] {request}")

        with Status("[cyan]AI is thinking...", console=self.console):
            try:
                # This would integrate with the AI model
                # For now, provide a placeholder response
                response = await self._generate_ai_response(request)

                self.console.print(Panel(
                    response,
                    title="🤖 AI Response",
                    border_style="blue"
                ))

                # Remember this interaction
                if self.memory_engine:
                    await self.memory_engine.smart_remember(
                        content=f"User: {request}\nAI: {response}",
                        memory_type="conversation",
                        importance=0.7
                    )

            except Exception as e:
                self.console.print(f"[red]AI request failed: {e}[/red]")

    async def _generate_ai_response(self, request: str) -> str:
        """Generate AI response (placeholder for actual AI integration)."""
        # This would integrate with the actual AI model
        # For now, return a helpful placeholder
        return (
            f"I understand you want help with: '{request}'\n\n"
            "This is where the AI model would provide intelligent assistance. "
            "The actual implementation would integrate with the loaded AI model "
            "to provide context-aware coding help, analysis, and suggestions."
        )

    def _is_likely_command(self, word: str) -> bool:
        """Check if a word is likely intended as a command."""
        # Check for common command patterns
        command_patterns = [
            r'^[a-z]+$',  # Simple lowercase words
            r'^[a-z]+-[a-z]+$',  # Hyphenated commands
            r'^[a-z]+_[a-z]+$',  # Underscore commands
        ]

        for pattern in command_patterns:
            if re.match(pattern, word):
                return True

        # Check similarity to existing commands
        close_matches = difflib.get_close_matches(word, self.commands.keys(), n=1, cutoff=0.6)
        return len(close_matches) > 0

    async def _handle_direct_model_load(self, args: List[str]):
        """Handle direct model loading command: 'load ModelName'."""
        if not args:
            await self._show_model_load_help()
            return

        model_name = args[0]
        await self._load_model(model_name)

    async def _show_model_load_help(self):
        """Show help for model loading."""
        self.console.print("[yellow]⚠️ Model name required[/yellow]")
        self.console.print("\n[cyan]Usage:[/cyan]")
        self.console.print("  [bold]load <model_name>[/bold]")
        self.console.print("  [bold]models load <model_name>[/bold]")
        self.console.print("\n[dim]Available models:[/dim]")
        await self._show_available_models()

    async def _show_command_error(self, command: str, error: str, args: List[str]):
        """Show detailed error information with suggestions."""
        self.console.print(f"[red]❌ Error executing '{command}':[/red] {error}")

        # Show command-specific help
        if command == "models":
            self.console.print("\n[cyan]💡 Models command usage:[/cyan]")
            self.console.print("  [bold]models list[/bold] - Show available models")
            self.console.print("  [bold]models load <name>[/bold] - Load a specific model")
            self.console.print("  [bold]models unload[/bold] - Unload current model")
            self.console.print("  [bold]models scan[/bold] - Scan for new models")
            self.console.print("  [bold]models info <name>[/bold] - Show model details")

        self.console.print(f"\n[dim]Type 'help {command}' for detailed information[/dim]")

    async def _handle_unknown_command(self, command: str, args: List[str]):
        """Handle unknown commands with intelligent suggestions."""
        # Find close matches
        close_matches = difflib.get_close_matches(command, self.commands.keys(), n=3, cutoff=0.6)

        self.console.print(f"[red]❌ Unknown command: '{command}'[/red]")

        if close_matches:
            self.console.print(f"\n[yellow]💡 Did you mean:[/yellow]")
            for match in close_matches:
                self.console.print(f"  [cyan]{match}[/cyan]")

        # Check for common typos and shortcuts
        suggestions = self._get_command_suggestions(command, args)
        if suggestions:
            self.console.print(f"\n[blue]🔍 Possible alternatives:[/blue]")
            for suggestion in suggestions:
                self.console.print(f"  [green]{suggestion}[/green]")

        self.console.print(f"\n[dim]Type 'help' to see all available commands[/dim]")

    def _get_command_suggestions(self, command: str, args: List[str]) -> List[str]:
        """Get intelligent command suggestions based on context."""
        suggestions = []

        # Common shortcuts and aliases
        shortcuts = {
            "ls": "models list",
            "list": "models list",
            "show": "models list",
            "start": "models load",
            "run": "models load",
            "stop": "models unload",
            "quit": "exit",
            "q": "exit",
            "h": "help",
            "?": "help",
        }

        if command in shortcuts:
            suggestions.append(shortcuts[command])

        # Context-based suggestions
        if command in ["model", "mod"]:
            suggestions.append("models list")
            if args:
                suggestions.append(f"models load {args[0]}")

        return suggestions

    # Command Handlers
    async def show_help(self, args: List[str]):
        """Show comprehensive help information with examples and tips."""
        if args and args[0] in self.commands:
            # Show specific command help
            await self._show_specific_command_help(args[0])
        else:
            # Show general help with categories
            await self._show_comprehensive_help()

    async def _show_specific_command_help(self, command: str):
        """Show detailed help for a specific command."""
        help_content = {
            "models": {
                "title": "🤖 Model Management",
                "description": "Manage AI models for code assistance",
                "usage": [
                    "models list - Show all available models",
                    "models load <name> - Load a specific model",
                    "models load <name> --device cuda - Load with GPU",
                    "models unload - Unload current model",
                    "models status - Show current model status",
                    "models info <name> - Show model details",
                    "models scan - Scan for new models",
                    "models switch <name> - Switch to different model"
                ],
                "shortcuts": [
                    "load <name> - Direct model loading",
                    "ls - List models (alias)"
                ],
                "examples": [
                    "models load Lucy-128k",
                    "load gpt-oss-20b-UD-Q8_K_XL --device cuda",
                    "models status"
                ]
            },
            "ai": {
                "title": "🤖 AI Assistant",
                "description": "Direct AI assistance for coding tasks",
                "usage": [
                    "ai <question> - Ask AI a question",
                    "ai help with Python - Get Python help",
                    "ai review this code - Code review request"
                ],
                "examples": [
                    "ai how do I implement a binary search?",
                    "ai explain this error message",
                    "ai suggest improvements for this function"
                ]
            },
            "code": {
                "title": "💻 Code Tools",
                "description": "AI-powered code generation and editing",
                "usage": [
                    "code create <description> - Generate new code",
                    "code edit <file> - Edit existing code",
                    "code analyze <file> - Analyze code quality",
                    "code review <file> - Review code"
                ],
                "examples": [
                    "code create a REST API endpoint",
                    "code analyze main.py",
                    "code review my_function.py"
                ]
            },
            "help": {
                "title": "❓ Help System",
                "description": "Get help and documentation",
                "usage": [
                    "help - Show all commands",
                    "help <command> - Show specific command help",
                    "? - Quick help (alias)"
                ],
                "examples": [
                    "help models",
                    "help ai",
                    "?"
                ]
            }
        }

        if command in help_content:
            info = help_content[command]

            # Create comprehensive help panel
            content = f"[bold]{info['description']}[/bold]\n\n"

            content += "[cyan]Usage:[/cyan]\n"
            for usage in info['usage']:
                content += f"  [green]{usage}[/green]\n"

            if 'shortcuts' in info:
                content += f"\n[cyan]Shortcuts:[/cyan]\n"
                for shortcut in info['shortcuts']:
                    content += f"  [yellow]{shortcut}[/yellow]\n"

            content += f"\n[cyan]Examples:[/cyan]\n"
            for example in info['examples']:
                content += f"  [dim]$ [/dim][white]{example}[/white]\n"

            help_panel = Panel(
                content,
                title=info['title'],
                border_style="blue"
            )
            self.console.print(help_panel)
        else:
            self.console.print(f"[yellow]No specific help available for '{command}'[/yellow]")
            self.console.print("[dim]Type 'help' to see all available commands[/dim]")

    async def _show_comprehensive_help(self):
        """Show comprehensive help with categories and quick start guide."""
        # Header with welcome message
        welcome_panel = Panel(
            "[bold blue]🚀 Welcome to Reverie CLI[/bold blue]\n\n"
            "[green]AI-Native Development Assistant[/green]\n"
            "Your intelligent coding companion with dual-mode capabilities:\n"
            "• [cyan]AI Coder Mode[/cyan] - Direct AI assistance for coding tasks\n"
            "• [cyan]API Service Mode[/cyan] - Server management and monitoring\n\n"
            "[yellow]💡 Quick Start:[/yellow]\n"
            "1. [white]models list[/white] - See available models\n"
            "2. [white]load <model_name>[/white] - Load a model\n"
            "3. [white]ai <your question>[/white] - Start coding with AI!",
            title="Getting Started",
            border_style="green"
        )
        self.console.print(welcome_panel)

        # Command categories
        categories = {
            "🤖 AI & Models": {
                "models": "Manage AI models (list, load, unload, status)",
                "load": "Quick model loading shortcut",
                "ai": "Direct AI assistance and chat",
                "code": "AI coding tools (create, edit, analyze)",
                "analyze": "Code analysis and insights",
                "review": "Code review and suggestions",
            },
            "🔧 Development Tools": {
                "test": "Generate and run tests",
                "refactor": "Code refactoring assistance",
                "search": "Intelligent search across memory",
                "remember": "Memory management",
                "context": "Context analysis",
            },
            "🌐 Web & External": {
                "web": "Web search and content retrieval",
                "server": "Server management",
                "api": "API endpoint management",
                "logs": "View server logs",
            },
            "⚙️ System & Utilities": {
                "status": "Show system status",
                "mode": "Switch between modes",
                "config": "Configuration management",
                "tools": "Available tools overview",
                "clear": "Clear console screen",
                "help": "Show help information",
                "exit": "Exit console",
            }
        }

        for category, commands in categories.items():
            table = Table(title=category, show_header=False)
            table.add_column("Command", style="cyan", width=15)
            table.add_column("Description", style="white")

            for cmd, desc in commands.items():
                table.add_row(cmd, desc)

            self.console.print(table)
            self.console.print()  # Add spacing

        # Tips and shortcuts
        tips_panel = Panel(
            "[bold yellow]💡 Pro Tips:[/bold yellow]\n\n"
            "• Use [cyan]Tab[/cyan] for auto-completion\n"
            "• Type [cyan]ls[/cyan] as shortcut for [cyan]models list[/cyan]\n"
            "• Use [cyan]load <model>[/cyan] for quick model loading\n"
            "• Type [cyan]?[/cyan] or [cyan]h[/cyan] for quick help\n"
            "• Commands support fuzzy matching for typos\n"
            "• Natural language works in AI mode: just type your question!\n\n"
            "[dim]Type 'help <command>' for detailed command information[/dim]",
            title="Tips & Shortcuts",
            border_style="yellow"
        )
        self.console.print(tips_panel)

    # Shortcut Command Handlers
    async def handle_direct_load(self, args: List[str]):
        """Handle direct load command shortcut."""
        if not args:
            await self._show_model_load_help()
        else:
            await self._load_model_with_options(args)

    async def handle_direct_unload(self, args: List[str]):
        """Handle direct unload command shortcut."""
        await self._unload_model()

    async def handle_list_shortcut(self, args: List[str]):
        """Handle 'ls' shortcut for models list."""
        await self._show_available_models()

    async def exit_console(self, args: List[str]):
        """Exit the console."""
        self.console.print("[yellow]Goodbye! 👋[/yellow]")
        self.running = False

    async def clear_screen(self, args: List[str]):
        """Clear the console screen."""
        self.console.clear()
        self._display_welcome()

    async def show_status(self, args: List[str]):
        """Show system status."""
        status_table = Table(title="🔍 System Status")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="white")
        status_table.add_column("Details", style="dim")

        # AI Coder Mode status
        ai_status = "✅ Active" if self.ai_coder_mode else "❌ Inactive"
        status_table.add_row("AI Coder Mode", ai_status, "Direct AI assistance")

        # API Service Mode status
        api_status = "✅ Active" if self.api_service_mode else "❌ Inactive"
        status_table.add_row("API Service Mode", api_status, "REST API endpoints")

        # Current model
        model_info = self.current_model or "No model loaded"
        status_table.add_row("Current Model", model_info, "AI model for assistance")

        # Enhanced engines
        web_status = "✅ Available" if self.web_engine else "❌ Unavailable"
        status_table.add_row("Web Engine", web_status, "Web search capabilities")

        context_status = "✅ Available" if self.context_engine else "❌ Unavailable"
        status_table.add_row("Context Engine", context_status, "Code analysis")

        memory_status = "✅ Available" if self.memory_engine else "❌ Unavailable"
        status_table.add_row("Memory Engine", memory_status, "Persistent memory")

        self.console.print(status_table)

    async def handle_ai_chat(self, args: List[str]):
        """Handle AI chat command."""
        if not args:
            self.console.print("[yellow]Usage: ai <your question or request>[/yellow]")
            return

        request = " ".join(args)
        await self._handle_natural_language_request(request)

    async def handle_ai_coding(self, args: List[str]):
        """Handle AI coding commands."""
        if not args:
            self.console.print("[yellow]Usage: code <create|edit|analyze|review|test> [options][/yellow]")
            return

        subcommand = args[0].lower()

        if subcommand == "create":
            await self._handle_code_creation(args[1:])
        elif subcommand == "edit":
            await self._handle_code_editing(args[1:])
        elif subcommand == "analyze":
            await self._handle_code_analysis(args[1:])
        elif subcommand == "review":
            await self._handle_code_review(args[1:])
        elif subcommand == "test":
            await self._handle_testing(args[1:])
        else:
            self.console.print(f"[red]Unknown code subcommand: {subcommand}[/red]")

    async def _handle_code_creation(self, args: List[str]):
        """Handle code creation requests."""
        self.console.print("[blue]🔨 AI Code Creation[/blue]")

        if not args:
            file_path = await self.session.prompt_async("Enter file path to create: ")
            description = await self.session.prompt_async("Describe what to create: ")
        else:
            file_path = args[0] if args else "new_file.py"
            description = " ".join(args[1:]) if len(args) > 1 else "Create a new file"

        self.console.print(f"[cyan]Creating:[/cyan] {file_path}")
        self.console.print(f"[cyan]Description:[/cyan] {description}")

        # This would integrate with actual AI code generation
        self.console.print("[green]✅ Code creation would be handled by AI model[/green]")

    async def handle_web_search(self, args: List[str]):
        """Handle web search commands."""
        if not args:
            query = await self.session.prompt_async("Enter search query: ")
        else:
            query = " ".join(args)

        if not self.web_engine:
            self.console.print("[red]Web engine not available[/red]")
            return

        self.console.print(f"[blue]🌐 Searching web for:[/blue] {query}")

        with Status("[cyan]Searching...", console=self.console):
            try:
                result = await self.web_engine.smart_search(query, max_results=5)

                if result.success:
                    results = result.data.get("results", [])

                    if results:
                        search_table = Table(title=f"🔍 Search Results for '{query}'")
                        search_table.add_column("Title", style="cyan", max_width=40)
                        search_table.add_column("URL", style="blue", max_width=50)
                        search_table.add_column("Snippet", style="white", max_width=60)

                        for item in results[:5]:
                            title = item.get("title", "No title")[:40]
                            url = item.get("url", "No URL")[:50]
                            snippet = item.get("snippet", "No snippet")[:60]
                            search_table.add_row(title, url, snippet)

                        self.console.print(search_table)
                    else:
                        self.console.print("[yellow]No results found[/yellow]")
                else:
                    self.console.print(f"[red]Search failed: {result.error}[/red]")

            except Exception as e:
                self.console.print(f"[red]Search error: {e}[/red]")

    async def handle_intelligent_search(self, args: List[str]):
        """Handle intelligent memory search."""
        if not args:
            query = await self.session.prompt_async("Enter search query: ")
        else:
            query = " ".join(args)

        if not self.memory_engine:
            self.console.print("[red]Memory engine not available[/red]")
            return

        self.console.print(f"[blue]🧠 Intelligent search for:[/blue] {query}")

        with Status("[cyan]Searching memory...", console=self.console):
            try:
                result = await self.memory_engine.intelligent_search(query, max_results=5)

                if result.success:
                    results = result.data.get("results", [])

                    if results:
                        for i, item in enumerate(results, 1):
                            item_data = item.get("item", {})
                            relevance = item.get("relevance_score", 0)

                            self.console.print(Panel(
                                f"[bold]Type:[/bold] {item_data.get('type', 'unknown')}\n"
                                f"[bold]Content:[/bold] {item_data.get('content', '')[:200]}...\n"
                                f"[bold]Relevance:[/bold] {relevance:.2f}\n"
                                f"[bold]Tags:[/bold] {', '.join(item_data.get('tags', []))}",
                                title=f"Result {i}",
                                border_style="cyan"
                            ))
                    else:
                        self.console.print("[yellow]No results found in memory[/yellow]")
                else:
                    self.console.print(f"[red]Search failed: {result.error}[/red]")

            except Exception as e:
                self.console.print(f"[red]Search error: {e}[/red]")

    async def handle_memory(self, args: List[str]):
        """Handle memory management commands."""
        if not args:
            self.console.print("[yellow]Usage: remember <content to remember>[/yellow]")
            return

        content = " ".join(args)

        if not self.memory_engine:
            self.console.print("[red]Memory engine not available[/red]")
            return

        self.console.print(f"[blue]🧠 Remembering:[/blue] {content[:100]}...")

        with Status("[cyan]Storing in memory...", console=self.console):
            try:
                result = await self.memory_engine.smart_remember(
                    content=content,
                    memory_type="user_input",
                    context=self.current_context,
                    importance=0.6
                )

                if result.success:
                    memory_id = result.data.get("memory_id")
                    self.console.print(f"[green]✅ Remembered with ID: {memory_id}[/green]")
                else:
                    self.console.print(f"[red]Failed to remember: {result.error}[/red]")

            except Exception as e:
                self.console.print(f"[red]Memory error: {e}[/red]")

    async def handle_context(self, args: List[str]):
        """Handle context analysis commands."""
        if not args:
            # Show current context
            self.console.print(f"[cyan]Current context:[/cyan] {self.current_context}")
            return

        subcommand = args[0].lower()

        if subcommand == "set":
            if len(args) > 1:
                self.current_context = args[1]
                self.console.print(f"[green]Context set to:[/green] {self.current_context}")
            else:
                new_context = await self.session.prompt_async("Enter new context: ")
                self.current_context = new_context
                self.console.print(f"[green]Context set to:[/green] {self.current_context}")

        elif subcommand == "analyze":
            if not self.context_engine:
                self.console.print("[red]Context engine not available[/red]")
                return

            project_path = args[1] if len(args) > 1 else "."

            self.console.print(f"[blue]🔍 Analyzing context for:[/blue] {project_path}")

            with Status("[cyan]Analyzing...", console=self.console):
                try:
                    result = await self.context_engine.smart_analyze(
                        project_path=project_path,
                        analysis_type="quick"
                    )

                    if result.success:
                        data = result.data

                        context_table = Table(title="📊 Context Analysis")
                        context_table.add_column("Metric", style="cyan")
                        context_table.add_column("Value", style="white")

                        context_table.add_row("Language", data.get("language", "Unknown"))
                        context_table.add_row("Framework", data.get("framework", "None"))
                        context_table.add_row("Symbols", str(data.get("symbol_count", 0)))
                        context_table.add_row("Relationships", str(data.get("relationship_count", 0)))

                        self.console.print(context_table)
                    else:
                        self.console.print(f"[red]Analysis failed: {result.error}[/red]")

                except Exception as e:
                    self.console.print(f"[red]Analysis error: {e}[/red]")
        else:
            self.console.print(f"[red]Unknown context subcommand: {subcommand}[/red]")

    # Placeholder handlers for other commands
    async def handle_code_analysis(self, args: List[str]):
        """Handle code analysis."""
        await self.handle_context(["analyze"] + args)

    async def handle_code_review(self, args: List[str]):
        """Handle code review."""
        self.console.print("[blue]🔍 Code Review[/blue]")
        self.console.print("[green]✅ Code review would be handled by AI model[/green]")

    async def handle_testing(self, args: List[str]):
        """Handle testing commands."""
        self.console.print("[blue]🧪 Testing[/blue]")
        self.console.print("[green]✅ Test generation would be handled by AI model[/green]")

    async def handle_refactoring(self, args: List[str]):
        """Handle refactoring commands."""
        self.console.print("[blue]♻️ Refactoring[/blue]")
        self.console.print("[green]✅ Refactoring would be handled by AI model[/green]")

    async def handle_models(self, args: List[str]):
        """Handle model management with enhanced intelligence."""
        if not args:
            # Show available models
            await self._show_available_models()
            return

        subcommand = args[0].lower()

        if subcommand == "list":
            await self._show_available_models()
        elif subcommand == "info":
            if len(args) > 1:
                await self._show_model_info(args[1])
            else:
                await self._show_models_info_help()
        elif subcommand == "load":
            if len(args) > 1:
                await self._load_model_with_options(args[1:])
            else:
                await self._show_model_load_help()
        elif subcommand == "unload":
            await self._unload_model()
        elif subcommand == "status":
            await self._show_model_status()
        elif subcommand == "scan":
            await self._scan_models()
        elif subcommand == "switch":
            if len(args) > 1:
                await self._switch_model(args[1])
            else:
                await self._show_model_switch_help()
        else:
            await self._show_models_help(subcommand)

    async def _show_available_models(self):
        """Show available models."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()
            models = model_manager.list_models()

            if not models:
                self.console.print("[yellow]No local models found.[/yellow]")
                self.console.print("[dim]To add models:[/dim]")
                self.console.print("[dim]1. Place GGUF files in: ./models/llm/gguf/ or ./models/llm/[/dim]")
                self.console.print("[dim]2. Place Transformers models in: ./models/llm/transformers/ or ./models/llm/[/dim]")
                self.console.print("[dim]3. Run 'models scan' to detect new models[/dim]")
                return

            models_table = Table(title="🤖 Available Models")
            models_table.add_column("Name", style="cyan", width=25)
            models_table.add_column("Backend", style="blue", width=12)
            models_table.add_column("Status", style="white", width=10)
            models_table.add_column("Size", style="dim", width=8)
            models_table.add_column("Context", style="dim", width=8)
            models_table.add_column("Description", style="white")

            for model in models:
                status_icon = {
                    "available": "⚪",
                    "loaded": "🟢",
                    "loading": "🟡",
                    "error": "🔴"
                }.get(model.status.value, "❓")

                size_str = f"{model.size_gb:.1f}GB" if model.size_gb else "Unknown"
                context_str = f"{model.context_length//1000}k" if model.context_length else "Unknown"

                models_table.add_row(
                    model.name,
                    model.backend.value,
                    f"{status_icon} {model.status.value}",
                    size_str,
                    context_str,
                    model.description or "No description"
                )

            self.console.print(models_table)

        except Exception as e:
            self.console.print(f"[red]❌ Error loading models: {e}[/red]")

    async def _load_model_with_options(self, args: List[str]):
        """Enhanced model loading with options parsing."""
        if not args:
            await self._show_model_load_help()
            return

        model_name = args[0]

        # Parse additional options
        options = {}
        i = 1
        while i < len(args):
            arg = args[i]
            if arg.startswith('--'):
                option_name = arg[2:]
                if i + 1 < len(args) and not args[i + 1].startswith('--'):
                    options[option_name] = args[i + 1]
                    i += 2
                else:
                    options[option_name] = True
                    i += 1
            else:
                i += 1

        await self._load_model_enhanced(model_name, options)

    async def _load_model_enhanced(self, model_name: str, options: Dict[str, Any] = None):
        """Enhanced model loading with better feedback and error handling."""
        if options is None:
            options = {}

        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()

            # Check if model exists
            available_models = model_manager.list_models()
            target_model = None

            for model in available_models:
                if model.name.lower() == model_name.lower():
                    target_model = model
                    break

            if not target_model:
                # Try fuzzy matching
                model_names = [m.name for m in available_models]
                close_matches = difflib.get_close_matches(model_name, model_names, n=3, cutoff=0.6)

                self.console.print(f"[red]❌ Model '{model_name}' not found[/red]")
                if close_matches:
                    self.console.print(f"\n[yellow]💡 Did you mean:[/yellow]")
                    for match in close_matches:
                        self.console.print(f"  [cyan]{match}[/cyan]")
                else:
                    self.console.print("\n[dim]Available models:[/dim]")
                    await self._show_available_models()
                return

            # Show loading progress
            with Status(f"[cyan]Loading model '{target_model.name}'...", console=self.console):
                # Check if already loaded
                if target_model.status.value == "loaded":
                    self.console.print(f"[yellow]⚠️ Model '{target_model.name}' is already loaded[/yellow]")
                    return

                # Load the model
                success = await model_manager.load_model(
                    target_model.name,
                    device=options.get('device', 'auto'),
                    **{k: v for k, v in options.items() if k != 'device'}
                )

                if success:
                    self.console.print(f"[green]✅ Model '{target_model.name}' loaded successfully![/green]")

                    # Show model info
                    current_model = model_manager.current_model_info
                    if current_model:
                        info_table = Table(title=f"🤖 Loaded Model: {current_model.name}")
                        info_table.add_column("Property", style="cyan")
                        info_table.add_column("Value", style="white")

                        info_table.add_row("Backend", current_model.backend.value)
                        info_table.add_row("Status", f"🟢 {current_model.status.value}")
                        if current_model.size_gb:
                            info_table.add_row("Size", f"{current_model.size_gb:.1f} GB")
                        if current_model.context_length:
                            info_table.add_row("Context", f"{current_model.context_length:,} tokens")

                        self.console.print(info_table)
                else:
                    self.console.print(f"[red]❌ Failed to load model '{target_model.name}'[/red]")
                    self.console.print("[dim]Check logs for more details[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Error loading model: {e}[/red]")
            self.logger.error(f"Model loading error: {e}")

    async def _show_model_status(self):
        """Show current model status."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()
            current_model = model_manager.current_model_info

            if current_model:
                status_table = Table(title="🤖 Current Model Status")
                status_table.add_column("Property", style="cyan")
                status_table.add_column("Value", style="white")

                status_icon = {
                    "available": "⚪",
                    "loaded": "🟢",
                    "loading": "🟡",
                    "error": "🔴"
                }.get(current_model.status.value, "❓")

                status_table.add_row("Name", current_model.name)
                status_table.add_row("Status", f"{status_icon} {current_model.status.value}")
                status_table.add_row("Backend", current_model.backend.value)
                if current_model.device:
                    status_table.add_row("Device", current_model.device)
                if current_model.size_gb:
                    status_table.add_row("Size", f"{current_model.size_gb:.1f} GB")
                if current_model.context_length:
                    status_table.add_row("Context Length", f"{current_model.context_length:,} tokens")

                self.console.print(status_table)
            else:
                self.console.print("[yellow]⚠️ No model currently loaded[/yellow]")
                self.console.print("[dim]Use 'models load <name>' to load a model[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Error getting model status: {e}[/red]")

    async def _switch_model(self, model_name: str):
        """Switch to a different model."""
        self.console.print(f"[blue]🔄 Switching to model '{model_name}'...[/blue]")

        # Unload current model first
        await self._unload_model()

        # Load new model
        await self._load_model_enhanced(model_name)

    async def _show_models_help(self, unknown_subcommand: str = None):
        """Show comprehensive models command help."""
        if unknown_subcommand:
            # Try to suggest similar commands
            subcommands = ["list", "load", "unload", "status", "scan", "info", "switch"]
            close_matches = difflib.get_close_matches(unknown_subcommand, subcommands, n=3, cutoff=0.6)

            self.console.print(f"[red]❌ Unknown models subcommand: '{unknown_subcommand}'[/red]")
            if close_matches:
                self.console.print(f"\n[yellow]💡 Did you mean:[/yellow]")
                for match in close_matches:
                    self.console.print(f"  [cyan]models {match}[/cyan]")

        # Show comprehensive help
        help_panel = Panel(
            "[bold cyan]Models Command Usage[/bold cyan]\n\n"
            "[green]models list[/green] - Show all available models\n"
            "[green]models load <name>[/green] - Load a specific model\n"
            "[green]models load <name> --device cuda[/green] - Load with specific device\n"
            "[green]models unload[/green] - Unload current model\n"
            "[green]models status[/green] - Show current model status\n"
            "[green]models info <name>[/green] - Show detailed model information\n"
            "[green]models scan[/green] - Scan for new models\n"
            "[green]models switch <name>[/green] - Switch to different model\n\n"
            "[dim]Shortcuts:[/dim]\n"
            "[yellow]load <name>[/yellow] - Direct model loading\n"
            "[yellow]ls[/yellow] - List models (alias for 'models list')",
            title="🤖 Model Management Help",
            border_style="blue"
        )
        self.console.print(help_panel)

    async def _show_models_info_help(self):
        """Show help for models info command."""
        self.console.print("[yellow]⚠️ Model name required for info command[/yellow]")
        self.console.print("\n[cyan]Usage:[/cyan] [bold]models info <model_name>[/bold]")
        self.console.print("\n[dim]Available models:[/dim]")
        await self._show_available_models()

    async def _show_model_switch_help(self):
        """Show help for model switching."""
        self.console.print("[yellow]⚠️ Model name required for switch command[/yellow]")
        self.console.print("\n[cyan]Usage:[/cyan] [bold]models switch <model_name>[/bold]")
        self.console.print("\n[dim]Available models:[/dim]")
        await self._show_available_models()

    async def _show_model_info(self, model_name: str):
        """Show detailed model information."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()
            model_info = model_manager.get_model_info(model_name)

            if not model_info:
                self.console.print(f"[red]Model not found: {model_name}[/red]")
                return

            info_text = f"""[bold cyan]Model Information: {model_name}[/bold cyan]

[yellow]Basic Info:[/yellow]
• Backend: {model_info.backend.value}
• Status: {model_info.status.value}
• Architecture: {model_info.architecture or 'Unknown'}
• Size: {model_info.size_gb:.1f}GB if model_info.size_gb else 'Unknown'
• Context Length: {model_info.context_length:,} if model_info.context_length else 'Unknown'

[yellow]Details:[/yellow]
• Parameters: {model_info.parameters:,} if model_info.parameters else 'Unknown'
• License: {model_info.license or 'Unknown'}
• Description: {model_info.description or 'No description'}

[yellow]Status:[/yellow]
• Loaded At: {model_info.loaded_at.strftime('%Y-%m-%d %H:%M:%S') if model_info.loaded_at else 'Not loaded'}
• Device: {model_info.device or 'Not specified'}
• Memory Usage: {model_info.memory_usage_gb:.1f}GB if model_info.memory_usage_gb else 'Unknown'
"""

            self.console.print(Panel(info_text, title="Model Details", border_style="blue"))

        except Exception as e:
            self.console.print(f"[red]Failed to get model info: {e}[/red]")

    async def _load_model(self, model_name: str):
        """Load a specific model."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()

            self.console.print(f"[blue]🔄 Loading model: {model_name}[/blue]")

            with Status("[cyan]Loading model...", console=self.console):
                model_info = await model_manager.load_model(model_name)

            self.console.print(f"[green]✅ Model loaded successfully: {model_name}[/green]")
            self.console.print(f"[dim]Backend: {model_info.backend.value}, Device: {model_info.device}[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Failed to load model: {e}[/red]")

    async def _unload_model(self):
        """Unload the current model."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()

            if not model_manager.current_model:
                self.console.print("[yellow]No model currently loaded[/yellow]")
                return

            model_name = model_manager.current_model_info.name
            self.console.print(f"[blue]🔄 Unloading model: {model_name}[/blue]")

            success = await model_manager.unload_model()

            if success:
                self.console.print(f"[green]✅ Model unloaded successfully: {model_name}[/green]")
            else:
                self.console.print(f"[red]❌ Failed to unload model: {model_name}[/red]")

        except Exception as e:
            self.console.print(f"[red]❌ Failed to unload model: {e}[/red]")

    async def _download_llama_cpp(self, version: str):
        """Download llama.cpp binaries."""
        try:
            from reverie_cli.models.llama_cpp_manager import get_llama_cpp_manager

            llama_manager = get_llama_cpp_manager()

            self.console.print(f"[blue]📥 Downloading llama.cpp version: {version}[/blue]")

            success = await llama_manager.download_and_install(version, force=False)

            if success:
                self.console.print(f"[green]✅ llama.cpp {version} installed successfully[/green]")
            else:
                self.console.print(f"[red]❌ Failed to install llama.cpp {version}[/red]")

        except Exception as e:
            self.console.print(f"[red]❌ Failed to download llama.cpp: {e}[/red]")

    async def _scan_models(self):
        """Scan for local models."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()

            self.console.print("[blue]🔍 Scanning for local models...[/blue]")

            with Status("[cyan]Scanning...", console=self.console):
                await model_manager.scan_local_models()

            models = model_manager.list_models()
            local_models = [m for m in models if m.name not in ["Menlo/Lucy-128k-gguf", "meta-llama/Llama-2-7b-chat-hf"]]

            self.console.print(f"[green]✅ Scan complete. Found {len(local_models)} local models.[/green]")

            if local_models:
                await self._show_available_models()

        except Exception as e:
            self.console.print(f"[red]❌ Failed to scan models: {e}[/red]")

    async def handle_server(self, args: List[str]):
        """Handle server management."""
        self.console.print("[blue]⚙️ Server Management[/blue]")
        self.console.print("[green]✅ Server management integration needed[/green]")

    async def handle_api(self, args: List[str]):
        """Handle API management."""
        self.console.print("[blue]🌐 API Management[/blue]")
        self.console.print("[green]✅ API management integration needed[/green]")

    async def handle_logs(self, args: List[str]):
        """Handle log viewing."""
        self.console.print("[blue]📋 Logs[/blue]")
        self.console.print("[green]✅ Log viewing integration needed[/green]")

    async def handle_tools(self, args: List[str]):
        """Handle tools management."""
        self.console.print("[blue]🔧 Tools[/blue]")
        self.console.print("[green]✅ Tools management integration needed[/green]")

    async def handle_config(self, args: List[str]):
        """Handle configuration."""
        self.console.print("[blue]⚙️ Configuration[/blue]")
        self.console.print("[green]✅ Configuration management integration needed[/green]")

    async def handle_mode_switch(self, args: List[str]):
        """Handle mode switching."""
        if not args:
            self.console.print(f"[cyan]Current modes:[/cyan]")
            self.console.print(f"  AI Coder Mode: {'✅ Active' if self.ai_coder_mode else '❌ Inactive'}")
            self.console.print(f"  API Service Mode: {'✅ Active' if self.api_service_mode else '❌ Inactive'}")
            return

        mode = args[0].lower()

        if mode == "ai":
            self.ai_coder_mode = not self.ai_coder_mode
            status = "enabled" if self.ai_coder_mode else "disabled"
            self.console.print(f"[green]AI Coder Mode {status}[/green]")
        elif mode == "api":
            self.api_service_mode = not self.api_service_mode
            status = "enabled" if self.api_service_mode else "disabled"
            self.console.print(f"[green]API Service Mode {status}[/green]")
        else:
            self.console.print(f"[red]Unknown mode: {mode}[/red]")
            self.console.print("[dim]Available modes: ai, api[/dim]")

    def _start_status_monitoring(self):
        """Start background status monitoring."""
        # Placeholder for background monitoring
        self.status_monitor_active = True

    async def _learn_from_interaction(self, user_input: str):
        """Learn from user interaction."""
        if self.memory_engine:
            try:
                await self.memory_engine.smart_remember(
                    content=f"User command: {user_input}",
                    memory_type="interaction",
                    context=self.current_context,
                    importance=0.3
                )
            except Exception as e:
                self.logger.warning(f"Failed to learn from interaction: {e}")

    def _cleanup(self):
        """Cleanup resources."""
        self.status_monitor_active = False
        self.console.print("[dim]Console cleanup completed[/dim]")


class InteractiveConsole:
    """Interactive console for Reverie CLI."""
    
    def __init__(self):
        self.console = Console()
        self.logger = get_logger("console")
        self.settings = get_settings()
        self.running = True

        # Available commands
        self.commands = {
            "help": self._cmd_help,
            "exit": self._cmd_exit,
            "quit": self._cmd_exit,
            "stop": self._cmd_stop,
            "status": self._cmd_status,
            "config": self._cmd_config,
            "models": self._cmd_models,
            "load": self._cmd_load_model,
            "unload": self._cmd_unload_model,
            "download": self._cmd_download_model,
            "chat": self._cmd_chat,
            "code": self._cmd_code,
            "memory": self._cmd_memory,
            "tools": self._cmd_tools,
            "clear": self._cmd_clear,
            "scan": self._cmd_scan_models,
        }

        # Setup prompt session with multiple fallbacks
        self.session = None
        try:
            # Try with history and completer
            self.session = PromptSession(
                history=FileHistory(str(Path.home() / ".reverie_history")),
                completer=self._create_completer(),
            )
        except Exception as e:
            self.logger.warning(f"Failed to create prompt session with history: {e}")
            try:
                # Try without history
                self.session = PromptSession(
                    completer=self._create_completer(),
                )
            except Exception as e2:
                self.logger.warning(f"Failed to create prompt session with completer: {e2}")
                try:
                    # Try minimal session
                    self.session = PromptSession()
                except Exception as e3:
                    self.logger.warning(f"Failed to create basic prompt session: {e3}")
                    # Will use input() fallback in run method
                    self.session = None
    
    def _create_completer(self):
        """Create enhanced command completer with model awareness."""
        return SmartReverieCompleter(self)
    
    def run(self):
        """Run the interactive console."""
        self.console.print("\n🤖 Reverie CLI Interactive Console", style="bold green")
        self.console.print("Type 'help' for available commands\n", style="dim")
        
        while self.running:
            try:
                # Get user input with fallback
                if self.session:
                    try:
                        user_input = self.session.prompt("reverie> ")
                    except Exception as prompt_error:
                        self.logger.warning(f"Prompt error: {prompt_error}")
                        # Fallback to simple input
                        try:
                            user_input = input("reverie> ")
                        except (EOFError, KeyboardInterrupt):
                            break
                else:
                    # Use simple input if session creation failed
                    try:
                        user_input = input("reverie> ")
                    except (EOFError, KeyboardInterrupt):
                        break

                if not user_input.strip():
                    continue

                # Parse command and arguments
                parts = user_input.strip().split()
                command = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []

                # Execute command
                if command in self.commands:
                    try:
                        self.commands[command](args)
                    except Exception as e:
                        self.console.print(f"❌ Error executing command: {e}", style="red")
                        self.logger.error(f"Command error: {e}")
                else:
                    self.console.print(f"❌ Unknown command: {command}", style="red")
                    self.console.print("Type 'help' for available commands", style="dim")

            except KeyboardInterrupt:
                self.console.print("\nUse 'exit' or 'quit' to leave the console", style="yellow")
            except EOFError:
                break
        
        self.console.print("👋 Goodbye!", style="yellow")
    
    def _cmd_help(self, args: List[str]):
        """Show enhanced help information."""
        if args and args[0] in self.commands:
            # Show specific command help
            self._show_specific_command_help(args[0])
        else:
            # Show comprehensive help overview
            self._show_comprehensive_help()

    def _show_comprehensive_help(self):
        """Show comprehensive help with categories and examples."""
        # Header
        self.console.print(Panel(
            "[bold blue]🚀 Reverie CLI Interactive Console[/bold blue]\n\n"
            "[green]AI-Native Development Assistant[/green]\n"
            "Combines powerful AI capabilities with traditional development tools\n\n"
            "[cyan]Usage:[/cyan] <command> [arguments]\n"
            "[dim]Type 'help <command>' for detailed information[/dim]",
            title="Welcome to Reverie CLI",
            border_style="blue"
        ))

        # Core Commands
        core_table = Table(title="🎯 Core Commands", show_header=True, header_style="bold green")
        core_table.add_column("Command", style="cyan", width=15)
        core_table.add_column("Usage", style="white", width=25)
        core_table.add_column("Description", style="dim")

        core_commands = [
            ("help", "help [command]", "Show help information"),
            ("status", "status", "System and server status"),
            ("config", "config", "Show configuration"),
            ("clear", "clear", "Clear console screen"),
            ("exit/quit", "exit", "Exit the console"),
        ]

        for cmd, usage, desc in core_commands:
            core_table.add_row(cmd, usage, desc)

        self.console.print(core_table)

        # AI and Model Commands
        ai_table = Table(title="🤖 AI & Model Management", show_header=True, header_style="bold blue")
        ai_table.add_column("Command", style="cyan", width=15)
        ai_table.add_column("Usage", style="white", width=25)
        ai_table.add_column("Description", style="dim")

        ai_commands = [
            ("models", "models", "List available models"),
            ("load", "load <model>", "Load a specific model"),
            ("unload", "unload", "Unload current model"),
            ("download", "download <item>", "Download models/tools"),
            ("chat", "chat", "Interactive AI chat mode"),
            ("code", "code <prompt>", "AI code generation"),
        ]

        for cmd, usage, desc in ai_commands:
            ai_table.add_row(cmd, usage, desc)

        self.console.print(ai_table)

        # System Commands
        system_table = Table(title="⚙️ System Management", show_header=True, header_style="bold yellow")
        system_table.add_column("Command", style="cyan", width=15)
        system_table.add_column("Usage", style="white", width=25)
        system_table.add_column("Description", style="dim")

        system_commands = [
            ("stop", "stop", "Stop the server"),
            ("memory", "memory", "Show memory usage"),
            ("tools", "tools", "List available tools"),
        ]

        for cmd, usage, desc in system_commands:
            system_table.add_row(cmd, usage, desc)

        self.console.print(system_table)

        # Enhanced Features
        self.console.print(Panel(
            "[bold cyan]✨ Enhanced Features:[/bold cyan]\n\n"
            "🧠 [bold]Local Model Support[/bold]\n"
            "   • GGUF models via llama.cpp\n"
            "   • Transformers models (HuggingFace)\n"
            "   • Automatic model type detection\n\n"
            "📁 [bold]Local Storage[/bold]\n"
            "   • Models stored in ./models/llm/\n"
            "   • No external cache dependencies\n"
            "   • Automatic llama.cpp download\n\n"
            "🔧 [bold]Developer Tools[/bold]\n"
            "   • Code analysis and generation\n"
            "   • Interactive AI assistance\n"
            "   • Memory and context management",
            title="Key Features",
            border_style="purple"
        ))

        # Quick Start
        self.console.print(Panel(
            "[bold green]🚀 Quick Start:[/bold green]\n\n"
            "1. [cyan]models[/cyan] - See available models\n"
            "2. [cyan]load <model>[/cyan] - Load a model\n"
            "3. [cyan]chat[/cyan] - Start AI conversation\n"
            "4. [cyan]code <prompt>[/cyan] - Generate code\n"
            "5. [cyan]status[/cyan] - Check system health\n\n"
            "[dim]💡 Tip: Use tab completion for commands[/dim]",
            title="Getting Started",
            border_style="green"
        ))
    
    def _cmd_exit(self, args: List[str]):
        """Exit the console."""
        self.running = False
    
    def _cmd_stop(self, args: List[str]):
        """Stop the server."""
        if confirm("Are you sure you want to stop the server?"):
            self.console.print("🛑 Stopping server...", style="yellow")

            try:
                # Try to gracefully shutdown the server
                import os
                import signal
                import sys

                # Get the current process ID
                pid = os.getpid()

                self.console.print(f"Sending shutdown signal to process {pid}...", style="dim")

                # Set running to False to exit the console loop
                self.running = False

                # Send SIGTERM to the current process to trigger graceful shutdown
                if hasattr(signal, 'SIGTERM'):
                    os.kill(pid, signal.SIGTERM)
                else:
                    # On Windows, use CTRL_C_EVENT
                    os.kill(pid, signal.CTRL_C_EVENT)

                self.console.print("✅ Server shutdown initiated", style="green")

            except Exception as e:
                self.console.print(f"❌ Failed to stop server gracefully: {e}", style="red")
                self.console.print("Exiting console...", style="yellow")
                self.running = False
    
    def _cmd_status(self, args: List[str]):
        """Show server and system status."""
        status_table = Table(title="System Status", show_header=True, header_style="bold green")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="white")
        status_table.add_column("Details", style="dim")
        
        # TODO: Get actual status from components
        status_table.add_row("Server", "🟢 Running", f"http://{self.settings.server.host}:{self.settings.server.port}")
        status_table.add_row("Model", "🟡 Loading", "Lucy-128k")
        status_table.add_row("Agent", "🟢 Ready", "Memory enabled")
        status_table.add_row("Database", "🟢 Connected", "SQLite")
        
        self.console.print(status_table)
    
    def _cmd_config(self, args: List[str]):
        """Show current configuration."""
        # Import here to avoid circular imports
        from reverie_cli.main import config
        config.callback()
    
    def _cmd_models(self, args: List[str]):
        """List available models."""
        try:
            from reverie_cli.models.manager import get_model_manager

            model_manager = get_model_manager()
            models = model_manager.list_models()

            if not models:
                self.console.print("[yellow]No local models found.[/yellow]")
                self.console.print("[dim]To add models:[/dim]")
                self.console.print("[dim]1. Place GGUF files in: ./models/llm/gguf/ or ./models/llm/[/dim]")
                self.console.print("[dim]2. Place Transformers models in: ./models/llm/transformers/ or ./models/llm/[/dim]")
                self.console.print("[dim]3. Run 'models' command again to see detected models[/dim]")
                return

            models_table = Table(title="Available Models", show_header=True, header_style="bold blue")
            models_table.add_column("Model", style="cyan", width=25)
            models_table.add_column("Backend", style="white", width=12)
            models_table.add_column("Status", style="white", width=12)
            models_table.add_column("Size", style="dim", width=8)
            models_table.add_column("Context", style="dim", width=8)

            for model in models:
                status_icon = {
                    "available": "⚪",
                    "loaded": "🟢",
                    "loading": "🟡",
                    "error": "🔴"
                }.get(model.status.value, "❓")

                size_str = f"{model.size_gb:.1f}GB" if model.size_gb else "Unknown"
                context_str = f"{model.context_length//1000}k" if model.context_length else "Unknown"

                models_table.add_row(
                    model.name[:24] + "..." if len(model.name) > 24 else model.name,
                    model.backend.value,
                    f"{status_icon} {model.status.value}",
                    size_str,
                    context_str
                )

            self.console.print(models_table)

        except Exception as e:
            self.console.print(f"[red]Failed to list models: {e}[/red]")
    
    def _cmd_load_model(self, args: List[str]):
        """Load a specific model."""
        if not args:
            self.console.print("❌ Please specify a model name", style="red")
            return

        model_name = " ".join(args)

        try:
            from reverie_cli.models.manager import get_model_manager
            import asyncio

            model_manager = get_model_manager()

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
            ) as progress:
                task = progress.add_task(f"Loading model: {model_name}", total=None)

                # Run async model loading
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    model_info = loop.run_until_complete(model_manager.load_model(model_name))
                    progress.update(task, description=f"✅ Model loaded: {model_name}")
                finally:
                    loop.close()

            self.console.print(f"🤖 Model '{model_name}' loaded successfully", style="green")
            self.console.print(f"Backend: {model_info.backend.value}, Device: {model_info.device}", style="dim")

        except Exception as e:
            self.console.print(f"❌ Failed to load model: {e}", style="red")
    
    def _cmd_unload_model(self, args: List[str]):
        """Unload current model."""
        try:
            from reverie_cli.models.manager import get_model_manager
            import asyncio

            model_manager = get_model_manager()

            if not model_manager.current_model:
                self.console.print("No model currently loaded", style="yellow")
                return

            model_name = model_manager.current_model_info.name
            self.console.print(f"🔄 Unloading model: {model_name}", style="yellow")

            # Run async model unloading
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(model_manager.unload_model())
            finally:
                loop.close()

            if success:
                self.console.print("✅ Model unloaded successfully", style="green")
            else:
                self.console.print("❌ Failed to unload model", style="red")

        except Exception as e:
            self.console.print(f"❌ Failed to unload model: {e}", style="red")
    
    def _cmd_download_model(self, args: List[str]):
        """Download a model or llama.cpp."""
        if not args:
            self.console.print("❌ Please specify what to download", style="red")
            self.console.print("Available: llama-cpp [version]", style="dim")
            return

        item = args[0].lower()

        if item == "llama-cpp":
            version = args[1] if len(args) > 1 else "latest"
            self._download_llama_cpp(version)
        else:
            self.console.print(f"❌ Unknown download item: {item}", style="red")
            self.console.print("Available: llama-cpp [version]", style="dim")

    def _download_llama_cpp(self, version: str):
        """Download llama.cpp binaries."""
        try:
            from reverie_cli.models.llama_cpp_manager import get_llama_cpp_manager
            import asyncio

            llama_manager = get_llama_cpp_manager()

            self.console.print(f"📥 Downloading llama.cpp version: {version}", style="blue")

            # Run async download
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(llama_manager.download_and_install(version, force=False))
            finally:
                loop.close()

            if success:
                self.console.print(f"✅ llama.cpp {version} installed successfully", style="green")
            else:
                self.console.print(f"❌ Failed to install llama.cpp {version}", style="red")

        except Exception as e:
            self.console.print(f"❌ Failed to download llama.cpp: {e}", style="red")
    
    def _cmd_chat(self, args: List[str]):
        """Start interactive chat mode."""
        self.console.print("💬 Starting chat mode (type 'exit' to return)", style="green")
        
        while True:
            try:
                message = self.session.prompt("chat> ", style="bold green")
                if message.lower() in ["exit", "quit"]:
                    break
                
                # TODO: Send message to AI model
                self.console.print(f"🤖 AI: I received your message: {message}", style="blue")
                
            except KeyboardInterrupt:
                break
        
        self.console.print("💬 Chat mode ended", style="yellow")
    
    def _cmd_code(self, args: List[str]):
        """Generate code with AI."""
        if not args:
            self.console.print("❌ Please provide a code generation prompt", style="red")
            return
        
        prompt = " ".join(args)
        self.console.print(f"🔧 Generating code for: {prompt}", style="blue")
        
        # TODO: Implement code generation
        code_panel = Panel(
            "# Generated code will appear here\nprint('Hello, World!')",
            title="Generated Code",
            border_style="green",
        )
        self.console.print(code_panel)
    
    def _cmd_memory(self, args: List[str]):
        """Show memory usage."""
        import psutil
        
        memory = psutil.virtual_memory()
        
        memory_table = Table(title="Memory Usage", show_header=True, header_style="bold yellow")
        memory_table.add_column("Metric", style="cyan")
        memory_table.add_column("Value", style="white")
        
        memory_table.add_row("Total", f"{memory.total / (1024**3):.1f} GB")
        memory_table.add_row("Available", f"{memory.available / (1024**3):.1f} GB")
        memory_table.add_row("Used", f"{memory.used / (1024**3):.1f} GB")
        memory_table.add_row("Percentage", f"{memory.percent:.1f}%")
        
        self.console.print(memory_table)
    
    def _cmd_tools(self, args: List[str]):
        """List available tools."""
        tools_table = Table(title="Available Tools", show_header=True, header_style="bold purple")
        tools_table.add_column("Tool", style="cyan")
        tools_table.add_column("Description", style="white")
        tools_table.add_column("Status", style="white")
        
        # TODO: Get actual tools from tool manager
        tools_table.add_row("file_operations", "Create, read, write, delete files", "🟢 Ready")
        tools_table.add_row("web_search", "Search the web for information", "🟢 Ready")
        tools_table.add_row("code_executor", "Execute code in various languages", "🟢 Ready")
        tools_table.add_row("git_operations", "Git repository management", "🟢 Ready")
        
        self.console.print(tools_table)
    
    def _cmd_clear(self, args: List[str]):
        """Clear the console."""
        self.console.clear()

    def _cmd_scan_models(self, args: List[str]):
        """Scan for local models."""
        try:
            from reverie_cli.models.manager import get_model_manager
            import asyncio

            model_manager = get_model_manager()

            self.console.print("🔍 Scanning for local models...", style="blue")

            # Run async model scanning
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(model_manager.scan_local_models())
            finally:
                loop.close()

            # Show results
            models = model_manager.list_models()
            if models:
                self.console.print(f"✅ Found {len(models)} local models", style="green")
                self._cmd_models([])  # Show the models table
            else:
                self.console.print("No local models found", style="yellow")
                self.console.print("[dim]To add models:[/dim]")
                self.console.print("[dim]1. Place GGUF files in: ./models/llm/gguf/ or ./models/llm/[/dim]")
                self.console.print("[dim]2. Place Transformers models in: ./models/llm/transformers/ or ./models/llm/[/dim]")

        except Exception as e:
            self.console.print(f"❌ Failed to scan models: {e}", style="red")
